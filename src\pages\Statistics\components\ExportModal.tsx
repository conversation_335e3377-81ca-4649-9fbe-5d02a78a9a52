import type { IExportOptions } from '@/types/export';
import { ExportFormat, ExportType } from '@/types/export';
import { DownloadOutlined, FileExcelOutlined } from '@ant-design/icons';
import {
  Card,
  Checkbox,
  Col,
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Space,
  Typography,
} from 'antd';
import React, { useEffect } from 'react';

const { Text } = Typography;
const { Option } = Select;

interface ExportModalProps {
  visible: boolean;
  loading?: boolean;
  currentMonth?: string;
  currentQuestionnaireId?: number;
  onCancel: () => void;
  onExport: (options: IExportOptions) => void;
}

/**
 * 导出配置模态框组件
 */
const ExportModal: React.FC<ExportModalProps> = ({
  visible,
  loading = false,
  onCancel,
  onExport,
}) => {
  const [form] = Form.useForm();

  // 导出类型选项
  const exportTypeOptions = [
    {
      value: ExportType.SCHOOL_STATISTICS,
      label: '学校统计数据',
      description: '包含学校整体统计信息、完成率、平均分等',
      icon: <FileExcelOutlined style={{ color: '#52c41a' }} />,
    },
    {
      value: ExportType.TEACHER_RANKING,
      label: '教师排名数据',
      description: '包含教师评分排行榜、排名信息等',
      icon: <FileExcelOutlined style={{ color: '#1890ff' }} />,
    },
    {
      value: ExportType.QUESTIONNAIRE_RESPONSES,
      label: '问卷响应数据',
      description: '包含问卷填写详情、评价内容等',
      icon: <FileExcelOutlined style={{ color: '#722ed1' }} />,
    },
    {
      value: ExportType.INCOMPLETE_STUDENTS,
      label: '未完成学生名单',
      description: '包含未填写问卷的学生信息',
      icon: <FileExcelOutlined style={{ color: '#fa8c16' }} />,
    },
    {
      value: ExportType.COMPREHENSIVE_REPORT,
      label: '综合报表',
      description: '包含完整的统计分析报告',
      icon: <FileExcelOutlined style={{ color: '#eb2f96' }} />,
    },
  ];

  // 处理导出类型变化
  const handleExportTypeChange = (type: ExportType) => {
    // 根据导出类型设置合适的默认选项
    const defaultOptions: Record<ExportType, any> = {
      [ExportType.SCHOOL_STATISTICS]: {
        includeTrend: true,
        includeTeacherRanking: true,
        includeIncompleteStudents: true,
      },
      [ExportType.TEACHER_STATISTICS]: {
        includeDistribution: true,
        includeKeywords: true,
        includeTrend: true,
      },
      [ExportType.TEACHER_RANKING]: {
        sortBy: 'average_score',
        sortOrder: 'DESC',
        limit: 100,
      },
      [ExportType.QUESTIONNAIRE_RESPONSES]: {
        includeAnswers: true,
      },
      [ExportType.COMPREHENSIVE_REPORT]: {
        includeSchoolSummary: true,
        includeTeacherRanking: true,
        includeGradeAnalysis: true,
        includeSubjectAnalysis: true,
        includeTrendAnalysis: true,
        includeCompletionAnalysis: true,
      },
      [ExportType.INCOMPLETE_STUDENTS]: {
        includeContactInfo: true,
      },
    };

    form.setFieldsValue(defaultOptions[type] || {});
  };

  // 处理确认导出
  const handleOk = () => {
    form.validateFields().then((values) => {
      const options: IExportOptions = {
        type: values.type,
        format: values.format || ExportFormat.XLSX,
        ...values,
      };
      onExport(options);
    });
  };

  // 渲染导出选项
  const renderExportOptions = (type: ExportType) => {
    switch (type) {
      case ExportType.SCHOOL_STATISTICS:
        return (
          <Card size="small" title="包含内容" style={{ marginTop: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="includeTrend" valuePropName="checked">
                  <Checkbox>趋势分析</Checkbox>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="includeTeacherRanking" valuePropName="checked">
                  <Checkbox>教师排名</Checkbox>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="includeIncompleteStudents"
                  valuePropName="checked"
                >
                  <Checkbox>未完成统计</Checkbox>
                </Form.Item>
              </Col>
            </Row>
          </Card>
        );

      case ExportType.TEACHER_RANKING:
        return (
          <Card size="small" title="筛选条件" style={{ marginTop: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="subject" label="学科">
                  <Input placeholder="请输入学科" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="department" label="部门">
                  <Input placeholder="请输入部门" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="limit" label="限制数量">
                  <InputNumber min={1} max={1000} placeholder="默认100" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="sortBy" label="排序字段">
                  <Select placeholder="请选择排序字段">
                    <Option value="average_score">平均分</Option>
                    <Option value="evaluation_count">评价人数</Option>
                    <Option value="sso_teacher_name">教师姓名</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="sortOrder" label="排序方向">
                  <Radio.Group>
                    <Radio value="DESC">降序</Radio>
                    <Radio value="ASC">升序</Radio>
                  </Radio.Group>
                </Form.Item>
              </Col>
            </Row>
          </Card>
        );

      case ExportType.QUESTIONNAIRE_RESPONSES:
        return (
          <Card size="small" title="筛选条件" style={{ marginTop: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="gradeCode" label="年级">
                  <Input placeholder="请输入年级代码" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="classCode" label="班级">
                  <Input placeholder="请输入班级代码" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="includeAnswers" valuePropName="checked">
                  <Checkbox>包含答案详情</Checkbox>
                </Form.Item>
              </Col>
            </Row>
          </Card>
        );

      case ExportType.INCOMPLETE_STUDENTS:
        return (
          <Card size="small" title="包含内容" style={{ marginTop: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="gradeCode" label="年级">
                  <Input placeholder="请输入年级代码" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="classCode" label="班级">
                  <Input placeholder="请输入班级代码" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="includeContactInfo" valuePropName="checked">
                  <Checkbox>包含联系信息</Checkbox>
                </Form.Item>
              </Col>
            </Row>
          </Card>
        );

      case ExportType.COMPREHENSIVE_REPORT:
        return (
          <Card size="small" title="包含内容" style={{ marginTop: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="includeSchoolSummary" valuePropName="checked">
                  <Checkbox>学校概览</Checkbox>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="includeTeacherRanking" valuePropName="checked">
                  <Checkbox>教师排名</Checkbox>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="includeGradeAnalysis" valuePropName="checked">
                  <Checkbox>年级分析</Checkbox>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="includeSubjectAnalysis"
                  valuePropName="checked"
                >
                  <Checkbox>学科分析</Checkbox>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="includeTrendAnalysis" valuePropName="checked">
                  <Checkbox>趋势分析</Checkbox>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="includeCompletionAnalysis"
                  valuePropName="checked"
                >
                  <Checkbox>完成情况分析</Checkbox>
                </Form.Item>
              </Col>
            </Row>
          </Card>
        );

      default:
        return null;
    }
  };

  // 初始化表单
  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        type: ExportType.SCHOOL_STATISTICS,
        format: ExportFormat.XLSX,
      });
    }
  }, [visible, form]);

  return (
    <Modal
      title={
        <Space>
          <DownloadOutlined />
          导出统计数据
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      onOk={handleOk}
      confirmLoading={loading}
      width={800}
      okText="开始导出"
      cancelText="取消"
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="type"
          label="导出类型"
          rules={[{ required: true, message: '请选择导出类型' }]}
        >
          <Radio.Group
            onChange={(e) => handleExportTypeChange(e.target.value)}
            style={{ width: '100%' }}
          >
            <Row gutter={16}>
              {exportTypeOptions.map((option) => (
                <Col span={12} key={option.value} style={{ marginBottom: 16 }}>
                  <Radio value={option.value} style={{ width: '100%' }}>
                    <Card
                      size="small"
                      style={{
                        marginLeft: 24,
                        cursor: 'pointer',
                      }}
                      bodyStyle={{ padding: '12px 16px' }}
                    >
                      <Space>
                        {option.icon}
                        <div>
                          <div style={{ fontWeight: 500 }}>{option.label}</div>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {option.description}
                          </Text>
                        </div>
                      </Space>
                    </Card>
                  </Radio>
                </Col>
              ))}
            </Row>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          name="format"
          label="导出格式"
          initialValue={ExportFormat.XLSX}
        >
          <Radio.Group>
            <Radio value={ExportFormat.XLSX}>Excel (.xlsx)</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item dependencies={['type']}>
          {({ getFieldValue }) => {
            const type = getFieldValue('type');
            return renderExportOptions(type);
          }}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ExportModal;
