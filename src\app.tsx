// 运行时配置

// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
import { DevTools } from '@/components';
import { LogoutOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  AntdConfig,
  RequestConfig,
  RunTimeLayoutConfig,
  RuntimeAntdConfig,
  request as httpRequest,
} from '@umijs/max';
import { Breadcrumb, Dropdown, message } from 'antd';
import {
  clearAuthData,
  getAccessToken,
  getUserInfo,
  logout,
  refreshToken,
} from './utils/auth';

// 声明 InitialState 类型
export type InitialState = API.User['userInfo'] | undefined;

// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
export async function getInitialState(): Promise<InitialState> {
  // 从本地存储获取用户信息
  const userInfo = getUserInfo();
  if (userInfo) {
    return userInfo;
  }

  return undefined;
}

export const layout: RunTimeLayoutConfig = ({ initialState }) => {
  return {
    logo: '/logo.png',
    menu: {
      locale: false,
    },
    layout: 'side',
    pure:
      !initialState || location.pathname.toLowerCase().startsWith('/parent'),
    actionsRender: () => null,
    // 侧边栏底部内容
    menuFooterRender: () => <DevTools />,
    childrenRender: (dom) => {
      if (location.pathname.toLowerCase().startsWith('/parent')) {
        return dom;
      }
      return (
        <PageContainer
          header={{
            title: '',
            breadcrumbRender: (props, defaultDom) => {
              return (
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}
                >
                  {defaultDom || (
                    <Breadcrumb
                      items={[
                        { title: (props as any)?.currentMenu?.name || ' ' },
                      ]}
                      style={{ paddingBlockStart: '18px' }}
                    />
                  )}
                  {initialState ? (
                    <Dropdown
                      menu={{
                        items: [
                          {
                            key: 'logout',
                            icon: <LogoutOutlined />,
                            label: '退出登录',
                            onClick: logout,
                          },
                        ],
                      }}
                    >
                      <div
                        style={{
                          paddingBlockStart: '18px',
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px',
                        }}
                      >
                        <img
                          src={initialState.avatar || '/avatar.png'}
                          style={{
                            width: '24px',
                            height: '24px',
                            borderRadius: '50%',
                            marginRight: '6px',
                          }}
                        />
                        <div>
                          {initialState.realName || initialState.username}
                        </div>
                        欢迎
                      </div>
                    </Dropdown>
                  ) : (
                    <div></div>
                  )}
                </div>
              );
            },
          }}
          childrenContentStyle={{ paddingInline: 0 }}
        >
          <div
            style={{
              height: 'calc(100vh - 98px)',
              overflowY: 'auto',
              paddingInline: '40px',
            }}
          >
            {dom}
          </div>
        </PageContainer>
      );
    },
    // antd layout全局样式
    token: {
      sider: {
        colorTextMenuTitle: '#eee',
        colorMenuBackground: '#253650',
        colorTextMenu: '#ccc',
        colorTextMenuItemHover: '#fff',
        colorTextMenuActive: '#fff',
        colorTextMenuSelected: '#fff',
        colorTextSubMenuSelected: '#fff',
        colorTextCollapsedButton: '#2f83ff',
        colorBgMenuItemHover: '#2f83ff',
        colorBgMenuItemActive: '#2f83ff',
        colorBgMenuItemSelected: '#2f83ff',
      },
      //   pageContainer: {
      //     paddingInlinePageContainerContent: 0,
      //     paddingBlockPageContainerContent: 0,
      //     colorBgPageContainer: 'rgb(203 226 255 / 60%)',
      //   },
    },
  };
};

export const antd: RuntimeAntdConfig = (
  memo: AntdConfig & { [key: string]: any },
) => {
  // 按需加载
  memo.import = true;

  // 配置 antd 的 App 包裹组件
  memo.appConfig = {
    // dark: true,
    message: {
      // 配置 message 最大显示数，超过限制时，最早的消息会被自动关闭
      maxCount: 1,
    },
  };

  memo.theme ??= {
    components: {
      Segmented: {
        itemSelectedBg: '#2979ff',
        itemSelectedColor: '#fff',
      },
      Collapse: {
        headerBg: '#2979ff',
      },
    },
  };
  memo.theme.token = {
    colorPrimary: '#2979ff', // 主题色
  };

  return memo;
};

export const request: RequestConfig = {
  baseURL: '/teacherEva_question_api',
  timeout: 10000,
  headers: { 'X-Requested-With': 'XMLHttpRequest' },
  errorConfig: {
    errorHandler: async (error: any) => {
      const { response, config } = error;

      // 添加一个标记，用于识别是否是重试请求
      if (config?._isRetry) {
        message.error(response?.data?.message || '请求失败');
        clearAuthData();
        return Promise.reject(error);
      }

      if (response?.status === 401) {
        // 如果是登录或刷新token接口，直接返回错误
        if (
          config?.url?.includes('/auth/login') ||
          config?.url?.includes('/auth/refresh')
        ) {
          message.error(
            response?.data?.msg || response?.data?.message || '登录失败',
          );
          return Promise.reject(error);
        }

        // 其他接口尝试刷新token
        try {
          const refreshSuccess = await refreshToken();
          if (refreshSuccess && config) {
            // 重试失败的请求
            const newAccessToken = getAccessToken();
            config.headers = config.headers || {};
            config.headers.Authorization = `Bearer ${newAccessToken}`;
            config._isRetry = true;

            return httpRequest(config.url, config);
          } else {
            // 刷新token失败，清除认证信息并跳转
            clearAuthData();
            message.error('登录已过期，请重新登录');
            // 跳转到未授权页面
            window.location.href = '/noAuth';
            return Promise.reject(error);
          }
        } catch (e) {
          // 刷新token失败，清除tokens
          clearAuthData();
          message.error('登录已过期，请重新登录');
          // 跳转到未授权页面
          window.location.href = '/noAuth';
          return Promise.reject(error);
        }
      } else {
        // 全局不显示错误提示，交给具体业务场景处理
        return Promise.reject(error);
      }
    },
  },
  requestInterceptors: [
    (config: any) => {
      const token = getAccessToken();
      if (token) {
        if (!config.headers) {
          config.headers = {};
        }
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
  ],
  responseInterceptors: [
    (response) => {
      // 处理header中的新token
      const newToken = response.headers['new-token'];
      if (newToken) {
        // 更新本地存储中的访问令牌
        localStorage.setItem(localStorage_prefix + 'accessToken', newToken);
      }

      // 检查响应数据中的errCode，处理业务层面的401认证失败
      // 但是跳过blob响应（如导出文件），因为blob数据不包含errCode
      if (
        response.data &&
        !(response.data instanceof Blob) &&
        (response.data as any).errCode === 401
      ) {
        // 创建一个401错误，让errorHandler处理
        const authError = new Error('Authentication failed');
        authError.name = 'AuthenticationError';
        // 模拟401响应结构
        const mockResponse = {
          status: 401,
          data: response.data,
        };
        // 抛出错误，触发errorHandler
        throw {
          response: mockResponse,
          config: { url: response.config?.url },
          message: (response.data as any).msg || '认证失败',
        };
      }

      return response;
    },
  ],
};
