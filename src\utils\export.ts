import type { IExportResponse, IExportResponseData } from '@/types/export';
import { message } from 'antd';
import dayjs from 'dayjs';

/**
 * 导出工具函数
 * @description 处理文件下载和导出相关功能
 */

/**
 * 从响应头中提取文件名
 * @param response 响应对象
 * @returns 文件名
 */
export const getFilenameFromResponse = (response: any): string => {
  const contentDisposition =
    response.headers?.['content-disposition'] ||
    response.headers?.['Content-Disposition'];

  if (contentDisposition) {
    // 尝试匹配 filename*=UTF-8''filename 格式
    const utf8Match = contentDisposition.match(/filename\*=UTF-8''(.+)/);
    if (utf8Match) {
      return decodeURIComponent(utf8Match[1]);
    }

    // 尝试匹配 filename="filename" 格式
    const normalMatch = contentDisposition.match(/filename="(.+)"/);
    if (normalMatch) {
      return normalMatch[1];
    }

    // 尝试匹配 filename=filename 格式
    const simpleMatch = contentDisposition.match(/filename=(.+)/);
    if (simpleMatch) {
      return simpleMatch[1];
    }
  }

  // 如果无法从响应头获取文件名，生成默认文件名
  return `export_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`;
};

/**
 * 下载文件（通过Blob）
 * @param blob 文件数据
 * @param filename 文件名
 */
export const downloadFile = (blob: Blob, filename: string) => {
  try {
    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    message.success(`文件 ${filename} 下载成功`);
  } catch (error) {
    console.error('文件下载失败:', error);
    message.error('文件下载失败，请重试');
  }
};

/**
 * 通过下载链接下载文件
 * @param downloadUrl 下载链接
 * @param filename 文件名
 */
export const downloadFileByUrl = (downloadUrl: string, filename: string) => {
  console.log('正在下载文件:', downloadUrl);
  try {
    // 创建下载链接
    const link = document.createElement('a');
    link.href = '/teacherEva_question_api' + downloadUrl;
    link.download = filename;
    link.target = '_blank'; // 在新窗口打开，避免页面跳转

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);

    message.success(`文件 ${filename} 下载成功`);
  } catch (error) {
    console.error('文件下载失败:', error);
    message.error('文件下载失败，请重试');
  }
};

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 处理导出响应（新版本 - 支持下载链接）
 * @param response 导出API响应
 * @param defaultFilename 默认文件名
 */
export const handleExportResponse = (
  response: IExportResponse | any,
  defaultFilename?: string,
) => {
  try {
    if (!response) {
      throw new Error('响应数据为空');
    }

    // 检查是否是新格式的响应（包含errCode的JSON响应）
    if (typeof response === 'object' && 'errCode' in response) {
      // 新格式：JSON响应包含下载链接
      if (response.errCode === 0 && response.data) {
        const exportData = response.data as IExportResponseData;
        const filename =
          exportData.filename ||
          defaultFilename ||
          `export_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`;

        // 使用下载链接下载文件
        downloadFileByUrl(exportData.downloadUrl, filename);

        // 记录文件信息
        console.log('导出成功，文件ID:', exportData.fileId);
        if (exportData.fileSize) {
          console.log('文件大小:', formatFileSize(exportData.fileSize));
        }
        if (exportData.expiresAt) {
          console.log('链接过期时间:', exportData.expiresAt);
        }

        return exportData;
      } else {
        throw new Error(response.msg || '导出失败');
      }
    }
    // 兼容旧格式：blob响应
    else if (response.data) {
      let blob: Blob;

      // 检查响应数据类型
      if (response.data instanceof Blob) {
        // 如果已经是 Blob，直接使用
        blob = response.data;
      } else if (response.data.type === 'Buffer' && response.data.data) {
        // 如果是 Buffer 类型，转换为 Uint8Array 再创建 Blob
        const uint8Array = new Uint8Array(response.data.data);
        blob = new Blob([uint8Array], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
      } else if (typeof response.data === 'string') {
        // 如果是字符串，可能是错误信息
        try {
          const errorData = JSON.parse(response.data);
          throw new Error(errorData.msg || '导出失败');
        } catch (parseError) {
          throw new Error('导出数据格式错误');
        }
      } else {
        // 其他情况，尝试直接创建 Blob
        blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
      }

      const filename =
        getFilenameFromResponse(response) ||
        defaultFilename ||
        `export_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`;

      downloadFile(blob, filename);
    } else {
      throw new Error('响应数据格式不正确');
    }
  } catch (error) {
    console.error('处理导出响应失败:', error);
    message.error('导出失败，请重试');
    throw error;
  }
};

/**
 * 生成导出文件名
 * @param type 导出类型
 * @param month 月份
 * @returns 文件名
 */
export const generateExportFilename = (
  type: string,
  month?: string,
): string => {
  const timestamp = dayjs().format('YYYY-MM-DD_HH-mm-ss');
  const monthStr = month ? `_${month}` : '';

  const typeMap: Record<string, string> = {
    school_statistics: '学校统计',
    teacher_statistics: '教师统计',
    teacher_ranking: '教师排名',
    questionnaire_responses: '问卷响应',
    comprehensive_report: '综合报表',
    incomplete_students: '未完成学生',
  };

  const typeName = typeMap[type] || '导出数据';
  return `${typeName}${monthStr}_${timestamp}.xlsx`;
};

/**
 * 验证导出参数
 * @param params 导出参数
 * @returns 验证结果
 */
export const validateExportParams = (
  params: any,
): { valid: boolean; message?: string } => {
  if (!params.sso_school_code) {
    return { valid: false, message: '学校代码不能为空' };
  }

  if (!params.month) {
    return { valid: false, message: '月份不能为空' };
  }

  if (!params.export_format) {
    return { valid: false, message: '导出格式不能为空' };
  }

  return { valid: true };
};

/**
 * 获取导出类型显示名称
 * @param type 导出类型
 * @returns 显示名称
 */
export const getExportTypeDisplayName = (type: string): string => {
  const typeMap: Record<string, string> = {
    school_statistics: '学校统计数据',
    teacher_statistics: '教师统计数据',
    teacher_ranking: '教师排名数据',
    questionnaire_responses: '问卷响应数据',
    comprehensive_report: '综合报表',
    incomplete_students: '未完成学生名单',
  };

  return typeMap[type] || '未知类型';
};

/**
 * 检查是否支持文件下载
 * @returns 是否支持
 */
export const isBrowserSupportDownload = (): boolean => {
  return !!(
    typeof window !== 'undefined' &&
    window.URL &&
    typeof window.URL.createObjectURL === 'function' &&
    typeof document !== 'undefined' &&
    typeof document.createElement === 'function'
  );
};
